# IDE相关文件
.idea/
.vscode/
*.swp
*.swo
*~

# 操作系统相关文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Node.js相关
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# 微信小程序相关
# 私有配置文件（包含个人开发者信息）
small/project.private.config.json
small/project.config.json

# 微信开发者工具生成的文件
small/miniprogram_npm/
small/.tea/

# 编译输出
small/dist/
small/build/


# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp
.cache/

# 环境配置文件（可能包含敏感信息）
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 后端相关（为将来的后端开发预留）
# Java
*.class
*.jar
*.war
*.ear
target/
.mvn/
mvnw
mvnw.cmd

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 配置文件备份
*.bak
*.backup

# 测试覆盖率报告
coverage/
.nyc_output/

# 其他
*.zip
*.tar.gz
*.rar


small/docs/
.cunzhi-memory